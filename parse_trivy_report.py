#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析Trivy安全扫描报告HTML文件，提取Package和Installed Version信息
按照package name + version作为唯一key进行去重
"""

import re
import json
import sys
from collections import OrderedDict
from html.parser import HTMLParser

class TrivyHTMLParser(HTMLParser):
    """自定义HTML解析器，用于解析Trivy报告"""

    def __init__(self):
        super().__init__()
        self.packages = OrderedDict()
        self.current_row_data = []
        self.in_severity_row = False
        self.in_td = False
        self.td_count = 0

    def handle_starttag(self, tag, attrs):
        if tag == 'tr':
            # 检查是否是包含漏洞信息的行
            for attr_name, attr_value in attrs:
                if attr_name == 'class' and 'severity-' in attr_value:
                    self.in_severity_row = True
                    self.current_row_data = []
                    self.td_count = 0
                    break
        elif tag == 'td' and self.in_severity_row:
            self.in_td = True

    def handle_endtag(self, tag):
        if tag == 'tr' and self.in_severity_row:
            # 处理完整的行数据
            if len(self.current_row_data) >= 4:
                package_name = self.current_row_data[0].strip()
                installed_version = self.current_row_data[3].strip()

                if package_name and installed_version:
                    if package_name not in self.packages:
                        self.packages[package_name] = installed_version
                        print(f"添加包: {package_name} -> {installed_version}")

            self.in_severity_row = False
            self.current_row_data = []
            self.td_count = 0
        elif tag == 'td' and self.in_td:
            self.in_td = False
            self.td_count += 1

    def handle_data(self, data):
        if self.in_td and self.in_severity_row:
            # 确保current_row_data有足够的元素
            while len(self.current_row_data) <= self.td_count:
                self.current_row_data.append("")
            self.current_row_data[self.td_count] += data

def parse_trivy_html(html_file_path):
    """
    解析Trivy HTML报告文件

    Args:
        html_file_path (str): HTML文件路径

    Returns:
        dict: 去重后的包信息，格式为 {package_name: version}
    """
    try:
        with open(html_file_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
    except FileNotFoundError:
        print(f"错误: 找不到文件 {html_file_path}")
        return {}
    except Exception as e:
        print(f"错误: 读取文件时出现异常 {e}")
        return {}

    # 使用自定义HTML解析器
    parser = TrivyHTMLParser()
    parser.feed(html_content)

    print(f"找到 {len(parser.packages)} 个唯一包")

    return parser.packages

def save_results(packages, output_format='json'):
    """
    保存解析结果

    Args:
        packages (dict): 包信息字典
        output_format (str): 输出格式，支持 'json', 'txt', 'csv'
    """
    if output_format == 'json':
        with open('packages.json', 'w', encoding='utf-8') as f:
            json.dump(packages, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到 packages.json")

    elif output_format == 'txt':
        with open('packages.txt', 'w', encoding='utf-8') as f:
            for package, version in packages.items():
                f.write(f"{package}\t{version}\n")
        print(f"结果已保存到 packages.txt")

    elif output_format == 'csv':
        with open('packages.csv', 'w', encoding='utf-8') as f:
            f.write("Package,Version\n")
            for package, version in packages.items():
                f.write(f'"{package}","{version}"\n')
        print(f"结果已保存到 packages.csv")

def main():
    """主函数"""
    html_file = "xander-test/ocloud-tcenter-mc-cas-portal.html"

    print("开始解析Trivy HTML报告...")
    packages = parse_trivy_html(html_file)

    if packages:
        print(f"\n解析完成！共找到 {len(packages)} 个唯一的包:")
        print("-" * 60)
        for package, version in packages.items():
            print(f"{package:<30} {version}")

        print(f"\n总计: {len(packages)} 个唯一包")

        # 保存结果到多种格式
        save_results(packages, 'json')
        save_results(packages, 'txt')
        save_results(packages, 'csv')

    else:
        print("未找到任何包信息")

if __name__ == "__main__":
    main()
