{"OpenIPMI": "2.0.19-15.tl2", "OpenIPMI-libs": "2.0.19-15.tl2", "OpenIPMI-modalias": "2.0.19-15.tl2", "apr-util": "1.5.2-6.tl2", "atk": "2.28.1-1.tl2", "audit": "2.7.6-3.tl2", "audit-libs": "2.7.6-3.tl2", "audit-libs-python": "2.7.6-3.tl2", "avahi": "0.6.31-17.tl2", "avahi-libs": "0.6.31-17.tl2", "bash": "4.2.46-33.tl2.1", "bind-export-libs": "32:9.11.4-9.P2.tl2", "bind-libs": "32:9.11.4-9.P2.tl2", "bind-libs-lite": "32:9.11.4-9.P2.tl2", "bind-license": "32:9.11.4-9.P2.tl2", "bind-utils": "32:9.11.4-9.P2.tl2", "binutils": "2.27-41.base.tl2.1", "binutils-devel": "2.27-41.base.tl2.1", "bison": "3.0.4-1.tl2", "blas": "3.4.2-8.tl2", "bzip2": "1.0.6-13.tl2", "bzip2-libs": "1.0.6-13.tl2", "ca-certificates": "2018.2.22-70.0.tl2", "cairo": "1.15.12-4.tl2", "chrony": "3.1-2.tl2", "compat-gcc-44": "4.4.7-8.tl2", "compat-gcc-44-c++": "4.4.7-8.tl2", "compat-glibc": "1:2.12-4.tl2", "compat-glibc-headers": "1:2.12-4.tl2", "compat-openldap": "1:2.3.43-5.tl2", "coreutils": "8.22-18.tl2", "cpio": "2.11-25.tl2", "cpp": "4.8.5-39.tl2.1", "cracklib": "2.9.0-11.tl2", "cracklib-dicts": "2.9.0-11.tl2", "cronie": "1.4.11-17.tl2", "cronie-anacron": "1.4.11-17.tl2", "ctags": "5.8-13.tl2", "cups-libs": "1:1.6.3-29.tl2", "curl": "7.29.0-54.tl2", "cvs": "1.11.23-35.tl2", "cvs-contrib": "1.11.23-35.tl2", "cyrus-sasl": "2.1.26-21.tl2", "cyrus-sasl-lib": "2.1.26-21.tl2", "cyrus-sasl-plain": "2.1.26-21.tl2", "dbus": "1:1.6.12-17.tl2", "dbus-libs": "1:1.6.12-17.tl2", "device-mapper": "7:1.02.158-2.tl2", "device-mapper-event": "7:1.02.158-2.tl2", "device-mapper-event-libs": "7:1.02.158-2.tl2", "device-mapper-libs": "7:1.02.158-2.tl2", "device-mapper-multipath": "0.4.9-111.tl2.2", "dhclient": "12:4.2.5-77.tl2", "dhcp-common": "12:4.2.5-77.tl2", "dhcp-libs": "12:4.2.5-77.tl2", "dmidecode": "1:3.0-5.tl2", "dosfstools": "3.0.20-9.tl2", "dracut": "047-8.git20180305.tl2.1", "dracut-network": "047-8.git20180305.tl2.1", "e2fsprogs": "1.42.9-10.tl2", "e2fsprogs-libs": "1.42.9-10.tl2", "ed": "1.9-4.tl2", "elfutils": "0.176-2.tl2", "elfutils-default-yama-scope": "0.176-2.tl2", "elfutils-libelf": "0.176-2.tl2", "elfutils-libs": "0.176-2.tl2", "emacs-filesystem": "1:24.3-22.tl2", "expat": "2.1.0-10.tl2", "file": "5.11-33.tl2", "file-libs": "5.11-33.tl2", "flex": "2.5.37-6.tl2", "freetype": "2.8-14.tl2", "fuse": "2.9.2-11.tl2", "fuse-libs": "2.9.2-11.tl2", "gawk": "4.0.2-4.tl2.1", "gcc": "4.8.5-39.tl2.1", "gcc-c++": "4.8.5-39.tl2.1", "gcc-gfortran": "4.8.5-39.tl2.1", "gcc-objc": "4.8.5-39.tl2.1", "gd": "2.0.35-26.tl2", "gdb": "7.6.1-100.tl2.1", "gdk-pixbuf2": "2.36.12-3.tl2", "gettext": "********-2.tl2", "gettext-common-devel": "********-2.tl2", "gettext-devel": "********-2.tl2", "gettext-libs": "********-2.tl2", "git": "*******-20.tl2", "glib2": "2.56.1-4.tl2", "glibc": "2.17-260.tl2", "glibc-common": "2.17-260.tl2", "glibc-devel": "2.17-260.tl2", "glibc-headers": "2.17-260.tl2", "glibc-static": "2.17-260.tl2", "gmp": "1:6.0.0-15.tl2", "gnupg2": "2.0.22-5.tl2", "gnutls": "3.3.26-9.tl2", "gpgme": "1.3.2-5.tl2", "graphite2": "1.3.10-1.tl2", "grub2": "1:2.02-0.65.tl2.2", "grub2-common": "1:2.02-0.65.tl2.2", "grub2-efi-x64": "1:2.02-0.65.tl2.2", "grub2-efi-x64-modules": "1:2.02-0.65.tl2.2", "grub2-pc": "1:2.02-0.65.tl2.2", "grub2-pc-modules": "1:2.02-0.65.tl2.2", "grub2-tools": "1:2.02-0.65.tl2.2", "grub2-tools-extra": "1:2.02-0.65.tl2.2", "grub2-tools-minimal": "1:2.02-0.65.tl2.2", "gtk-update-icon-cache": "3.22.10-5.tl2", "gtk2": "2.24.31-1.tl2", "gzip": "1.5-9.tl2", "harfbuzz": "1.7.5-2.tl2", "hunspell": "1.3.2-15.tl2", "iproute": "3.10.0-87.tl2", "iptables": "1.4.21-18.3.tl2", "iptraf-ng": "1.1.4-6.tl2", "iputils": "20160308-10.tl2", "iwl105-firmware": "**********-72.tl2", "iwl135-firmware": "**********-72.tl2", "iwl2000-firmware": "**********-72.tl2", "iwl2030-firmware": "**********-72.tl2", "iwl3160-firmware": "********-72.tl2", "iwl7260-firmware": "********-72.tl2", "iwl7265-firmware": "********-72.tl2", "jasper-libs": "1.900.1-33.tl2", "json-c": "0.11-4.tl2", "kernel-headers": "4.14.105-19.0009.tl2", "krb5-devel": "1.15.1-37.tl2", "krb5-libs": "1.15.1-37.tl2", "krb5-workstation": "1.15.1-37.tl2", "ksh": "20120801-35.tl2", "lapack": "3.4.2-8.tl2", "less": "458-9.tl2", "lftp": "4.4.8-8.tl2.2", "libX11": "1.6.7-2.tl2", "libX11-common": "1.6.7-2.tl2", "libXcursor": "1.1.15-1.tl2", "libXfixes": "5.0.3-1.tl2", "libXfont": "1.5.4-1.tl2", "libXi": "1.7.9-1.tl2", "libXpm": "3.5.12-1.tl2", "libXrandr": "1.5.1-2.tl2", "libXrender": "0.9.10-1.tl2", "libXtst": "1.2.3-1.tl2", "libblkid": "2.23.2-61.tl2", "libcap": "2.22-9.tl2", "libcom_err": "1.42.9-10.tl2", "libcom_err-devel": "1.42.9-10.tl2", "libcroco": "0.6.12-4.tl2", "libcurl": "7.29.0-54.tl2", "libdb": "5.3.21-21.tl2", "libdb-devel": "5.3.21-21.tl2", "libdb-utils": "5.3.21-21.tl2", "libevent": "2.0.21-4.tl2", "libgcc": "4.8.5-39.tl2.1", "libgcrypt": "1.5.3-14.tl2", "libgfortran": "4.8.5-39.tl2.1", "libgomp": "4.8.5-39.tl2.1", "libidn": "1.28-4.tl2", "libjpeg-turbo": "1.2.90-8.tl2", "libjpeg-turbo-utils": "1.2.90-8.tl2", "libkadm5": "1.15.1-37.tl2", "libmount": "2.23.2-61.tl2", "libnl": "1.1.4-3.tl2", "libobjc": "4.8.5-39.tl2.1", "libpcap": "14:1.5.3-11.tl2", "libpng": "2:1.5.13-7.tl2", "libquadmath": "4.8.5-39.tl2.1", "libquadmath-devel": "4.8.5-39.tl2.1", "librados2": "1:0.94.5-2.tl2", "libseccomp": "2.3.1-3.tl2", "libsepol": "2.5-10.tl2", "libsepol-devel": "2.5-10.tl2", "libss": "1.42.9-10.tl2", "libssh2": "1.4.3-10.tl2.1", "libstdc++": "4.8.5-39.tl2.1", "libstdc++-devel": "4.8.5-39.tl2.1", "libtasn1": "4.10-1.tl2", "libtiff": "4.0.3-32.tl2", "libtiff-tools": "4.0.3-32.tl2", "libtirpc": "0.2.4-0.16.tl2", "libuuid": "2.23.2-61.tl2", "libwayland-client": "1.15.0-1.tl2", "libwayland-server": "1.15.0-1.tl2", "libxml2": "2.9.1-6.tl2.3", "libxml2-python": "2.9.1-6.tl2.3", "libxslt": "1.1.28-5.tl2", "linux-firmware": "20190429-72.gitddde598.tl2", "lua": "5.1.4-15.tl2", "lvm2": "7:2.02.185-2.tl2", "lvm2-libs": "7:2.02.185-2.tl2", "lz4": "1.7.5-2.tl2", "m2crypto": "0.21.1-17.tl2", "mariadb": "1:5.5.60-1.tl2", "mariadb-libs": "1:5.5.60-1.tl2", "mdadm": "4.0-5.tl2", "mesa-dri-drivers": "18.3.4-5.tl2", "mesa-filesystem": "18.3.4-5.tl2", "mesa-libEGL": "18.3.4-5.tl2", "mesa-libGL": "18.3.4-5.tl2", "mesa-libgbm": "18.3.4-5.tl2", "mesa-libglapi": "18.3.4-5.tl2", "mgetty": "1.1.36-28.tl2", "microcode_ctl": "2:2.1-53.2.tl2", "motif": "2.3.4-12.tl2", "mpfr": "3.1.1-4.tl2", "ncurses": "5.9-14.20130511.tl2", "ncurses-base": "5.9-14.20130511.tl2", "ncurses-devel": "5.9-14.20130511.tl2", "ncurses-libs": "5.9-14.20130511.tl2", "net-snmp-libs": "1:5.7.2-28.tl2.1", "nettle": "2.7.1-8.tl2", "nscd": "2.17-260.tl2", "nspr": "4.21.0-1.tl2", "nss": "3.44.0-4.tl2", "nss-softokn": "3.44.0-5.tl2", "nss-softokn-freebl": "3.44.0-5.tl2", "nss-sysinit": "3.44.0-4.tl2", "nss-tools": "3.44.0-4.tl2", "nss-util": "3.44.0-3.tl2", "ntp": "4.2.6p5-29.tl2", "ntpdate": "4.2.6p5-29.tl2", "numpy": "1:1.7.1-11.tl2", "oddjob": "0.31.5-4.tl2", "openldap": "2.4.44-21.tl2", "openldap-clients": "2.4.44-21.tl2", "openssh": "7.4p1-21.tl2.1", "openssh-clients": "7.4p1-21.tl2.1", "openssh-server": "7.4p1-21.tl2.1", "openssl": "1:1.0.2k-19.tl2", "openssl-libs": "1:1.0.2k-19.tl2", "openssl098e": "0.9.8e-29.tl2.3", "p11-kit": "0.23.5-3.tl2", "p11-kit-trust": "0.23.5-3.tl2", "pam": "1.1.8-22.1.tl2", "pango": "1.40.4-1.tl2", "patch": "2.7.1-12.tl2", "pcre": "8.32-17.tl2", "pcre-devel": "8.32-17.tl2", "pcre-tools": "8.32-17.tl2", "pcsc-lite-libs": "1.8.8-6.tl2", "perf": "4.14.105-19.0009.tl2", "perl": "4:5.16.3-294.tl2", "perl-Archive-Zip": "1.30-11.tl2", "perl-CPAN": "1.9800-294.tl2", "perl-DBD-MySQL": "4.023-5.tl2", "perl-DBI": "1.627-4.tl2", "perl-Data-Dumper": "2.145-3.tl2", "perl-ExtUtils-CBuilder": "1:********-294.tl2", "perl-ExtUtils-Embed": "1.30-294.tl2", "perl-ExtUtils-Install": "1.58-294.tl2", "perl-File-Path": "2.09-2.tl2", "perl-Git": "*******-20.tl2", "perl-HTTP-Daemon": "6.01-5.tl2", "perl-HTTP-Tiny": "0.033-3.tl2", "perl-IO-Zlib": "1:1.10-294.tl2", "perl-Image-Info": "1.33-3.tl2", "perl-Locale-Maketext-Simple": "1:0.21-294.tl2", "perl-Module-CoreList": "1:2.76.02-294.tl2", "perl-Module-Loaded": "1:0.08-294.tl2", "perl-Object-Accessor": "1:0.42-294.tl2", "perl-Package-Constants": "1:0.02-294.tl2", "perl-PlRPC": "0.2020-14.tl2", "perl-Pod-Escapes": "1:1.04-294.tl2", "perl-Time-Piece": "1.20.1-294.tl2", "perl-XML-LibXML": "1:2.0018-5.tl2", "perl-core": "5.16.3-294.tl2", "perl-devel": "4:5.16.3-294.tl2", "perl-libs": "4:5.16.3-294.tl2", "perl-libwww-perl": "6.05-2.tl2", "perl-macros": "4:5.16.3-294.tl2", "pixman": "0.34.0-1.tl2", "plymouth": "0.8.9-0.28.20140113.tl2", "plymouth-core-libs": "0.8.9-0.28.20140113.tl2", "plymouth-scripts": "0.8.9-0.28.20140113.tl2", "polkit": "0.112-22.tl2.1", "postgresql-libs": "9.2.24-1.tl2", "procps-ng": "3.3.10-26.tl2", "python": "2.7.5-86.tl2.1", "python-configobj": "4.7.2-7.tl2", "python-ipaddress": "1.0.16-2.tl2", "python-ldap": "2.4.15-2.tl2", "python-libs": "2.7.5-86.tl2.1", "python-lxml": "3.2.1-4.tl2", "python-perf": "4.14.105-19.0009.tl2", "python-requests": "2.6.0-5.tl2", "python-setuptools": "0.9.8-7.tl2", "python-urllib3": "1.10.2-7.tl2", "qemu-guest-agent": "10:2.8.0-2.tl2", "readline": "6.2-10.tl2", "rpcbind": "0.2.0-48.tl2", "rpm": "4.11.3-40.tl2", "rpm-build": "4.11.3-40.tl2", "rpm-build-libs": "4.11.3-40.tl2", "rpm-libs": "4.11.3-40.tl2", "rpm-python": "4.11.3-40.tl2", "rpm-sign": "4.11.3-40.tl2", "rsh": "0.17-76.tl2.1", "rsync": "3.0.9-18.tl2", "rsyslog": "8.24.0-41.tl2.4", "screen": "4.1.0-0.23.20120314git3c2946.tl2", "selinux-policy": "3.13.1-229.tl2.9", "selinux-policy-devel": "3.13.1-229.tl2.9", "selinux-policy-targeted": "3.13.1-229.tl2.9", "shadow-utils": "2:*******-25.tl2", "shared-mime-info": "1.8-3.tl2", "sqlite": "3.7.17-8.tl2", "sudo": "1.8.23-4.tl2", "sysstat": "10.1.5-18.tl2", "systemd": "219-67.tl2.3", "systemd-libs": "219-67.tl2.3", "systemd-sysv": "219-67.tl2.3", "tar": "2:1.26-32.tl2", "tcpdump": "14:4.9.2-4.tl2", "traceroute": "3:2.0.22-2.tl2", "trousers": "0.3.14-2.tl2", "tuned": "2.8.0-5.tl2.2", "unzip": "6.0-20.tl2", "util-linux": "2.23.2-61.tl2", "vim-common": "2:7.4.629-6.tl2", "vim-enhanced": "2:7.4.629-6.tl2", "vim-filesystem": "2:7.4.629-6.tl2", "vim-minimal": "2:7.4.629-6.tl2", "wget": "1.14-18.tl2.1", "xz": "5.2.2-1.tl2", "xz-libs": "5.2.2-1.tl2", "xz-lzma-compat": "5.2.2-1.tl2", "zlib": "1.2.7-17.tl2", "zlib-devel": "1.2.7-17.tl2", "zlib-static": "1.2.7-17.tl2", "zsh": "5.0.2-33.tl2", "com.fasterxml.jackson.core:jackson-databind": "********", "com.google.code.gson:gson": "2.8.5", "com.google.guava:guava": "18.0", "com.squareup.okio:okio": "1.8.0", "commons-beanutils:commons-beanutils": "1.8.3", "commons-httpclient:commons-httpclient": "3.1", "commons-io:commons-io": "2.4", "dom4j:dom4j": "1.6.1", "mysql:mysql-connector-java": "5.1.21", "org.apache.httpcomponents:httpclient": "4.4.1", "org.apache.shiro:shiro-core": "1.10.0", "org.bitbucket.b_c:jose4j": "0.4.1", "org.bouncycastle:bcprov-jdk15on": "1.50", "org.cryptacular:cryptacular": "1.0", "org.hibernate:hibernate-core": "4.3.10.Final", "org.quartz-scheduler:quartz": "2.2.1", "org.springframework.security:spring-security-core": "4.0.1.RELEASE", "org.springframework.security:spring-security-web": "4.0.1.RELEASE", "org.springframework:spring-beans": "4.1.8.RELEASE", "org.springframework:spring-context": "4.1.8.RELEASE", "org.springframework:spring-core": "4.1.8.RELEASE", "org.springframework:spring-expression": "4.1.8.RELEASE", "org.springframework:spring-web": "4.1.8.RELEASE", "org.springframework:spring-webmvc": "4.1.8.RELEASE", "M2Crypto": "0.21.1", "configobj": "4.7.2", "lxml": "3.2.1", "requests": "2.6.0", "setuptools": "0.9.8", "urllib3": "1.10.2"}