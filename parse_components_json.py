#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解析422795.json文件，提取所有component的name和version信息
按照component name + version作为唯一key进行去重
"""

import json
import sys
from collections import OrderedDict

def parse_components_json(json_file_path):
    """
    解析JSON文件中的components信息
    
    Args:
        json_file_path (str): JSON文件路径
        
    Returns:
        dict: 去重后的组件信息，格式为 {component_name: version}
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误: 找不到文件 {json_file_path}")
        return {}
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败 {e}")
        return {}
    except Exception as e:
        print(f"错误: 读取文件时出现异常 {e}")
        return {}
    
    # 存储去重后的组件信息
    components = OrderedDict()
    
    # 检查数据结构
    if not isinstance(data, dict) or 'data' not in data:
        print("错误: JSON文件格式不正确，缺少'data'字段")
        return {}
    
    data_list = data['data']
    if not isinstance(data_list, list):
        print("错误: 'data'字段应该是一个列表")
        return {}
    
    total_components = 0
    
    # 遍历data列表中的每个元素
    for item in data_list:
        if not isinstance(item, dict) or 'components' not in item:
            continue
            
        components_list = item['components']
        if not isinstance(components_list, list):
            continue
            
        # 遍历components列表
        for component in components_list:
            if not isinstance(component, dict):
                continue
                
            # 提取name和version
            name = component.get('name', '').strip()
            version = component.get('version', '').strip()
            
            if name and version:
                total_components += 1
                # 使用component name作为key，如果已存在则不覆盖（保持第一次出现的版本）
                if name not in components:
                    components[name] = version
                    print(f"添加组件: {name} -> {version}")
    
    print(f"总共处理了 {total_components} 个组件记录")
    print(f"去重后得到 {len(components)} 个唯一组件")
    
    return components

def save_results(components, output_format='json'):
    """
    保存解析结果
    
    Args:
        components (dict): 组件信息字典
        output_format (str): 输出格式，支持 'json', 'txt', 'csv'
    """
    if output_format == 'json':
        with open('components.json', 'w', encoding='utf-8') as f:
            json.dump(components, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到 components.json")
    
    elif output_format == 'txt':
        with open('components.txt', 'w', encoding='utf-8') as f:
            for component, version in components.items():
                f.write(f"{component}\t{version}\n")
        print(f"结果已保存到 components.txt")
    
    elif output_format == 'csv':
        with open('components.csv', 'w', encoding='utf-8') as f:
            f.write("Component,Version\n")
            for component, version in components.items():
                f.write(f'"{component}","{version}"\n')
        print(f"结果已保存到 components.csv")

def main():
    """主函数"""
    json_file = "xander-test/422795.json"
    
    print("开始解析422795.json文件...")
    components = parse_components_json(json_file)
    
    if components:
        print(f"\n解析完成！共找到 {len(components)} 个唯一的组件:")
        print("-" * 60)
        for component, version in components.items():
            print(f"{component:<40} {version}")
        
        print(f"\n总计: {len(components)} 个唯一组件")
        
        # 保存结果到多种格式
        save_results(components, 'json')
        save_results(components, 'txt')
        save_results(components, 'csv')
        
    else:
        print("未找到任何组件信息")

if __name__ == "__main__":
    main()
